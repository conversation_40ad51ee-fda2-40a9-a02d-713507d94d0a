import json

from prime_service_kit import BaseServiceError
from prime_shared.common_types import AccountIdType, SourceIdType
from starlette import status

from .error_codes import ErrorCode


class DesignDocDuplicateError(BaseServiceError):
    http_status_code = status.HTTP_409_CONFLICT
    error_type = ErrorCode.DesignDocDuplicateError.name
    is_reliability_issue = False

    def __init__(self, names: list[str]):
        self._names = names
        super().__init__()

    def _get_msg(self) -> str:
        return json.dumps(self._names)


class DesignDocAttachContextError(BaseServiceError):
    http_status_code = status.HTTP_400_BAD_REQUEST
    error_type = ErrorCode.DesignDocAttachContextError.name
    is_reliability_issue = False

    def __init__(self, doc_id: int, context_id: int):
        self._doc_id = doc_id
        self._context_id = context_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Error attaching context to design doc {self._doc_id} with context id {self._context_id}"


class DesignDocNotFoundError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.DesignDocNotFoundError.name
    is_reliability_issue = False

    def __init__(self, doc_identifier: int | str):
        self._doc_identifier = doc_identifier
        super().__init__()

    def _get_msg(self) -> str:
        return f"Design document with identifier {self._doc_identifier} not found"


class DesignDocUpdateNotAllowed(BaseServiceError):
    http_status_code = status.HTTP_400_BAD_REQUEST
    error_type = ErrorCode.DesignDocUpdateNotAllowed.name
    is_reliability_issue = False

    def __init__(self, doc_id: int, reason: str):
        self._doc_id = doc_id
        self._reason = reason
        super().__init__()

    def _get_msg(self) -> str:
        return f"Design document with ID {self._doc_id} cannot be updated: {self._reason}"


class AIGenerationTimeoutError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.AIGenerationTimeoutError.name
    is_reliability_issue = False

    def __init__(self, issue_id: str):
        self._issue_id = issue_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"Timeout waiting for AI generation for issue {self._issue_id}"


class AIGenerationError(BaseServiceError):
    http_status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    error_type = ErrorCode.AIGenerationError.name
    is_reliability_issue = False

    def _get_msg(self) -> str:
        return (
            f"AI generation error for account {self._account_id}, source {self._source_id}, "
            f"issue {self._issue_id}: {self._error}"
        )

    def __init__(self, account_id: AccountIdType, source_id: SourceIdType, issue_id: str, error: str = ""):
        self._account_id = account_id
        self._source_id = source_id
        self._issue_id = issue_id
        self._error = error
        super().__init__()


class JobNotStartedError(Exception):
    def __init__(self) -> None:
        super().__init__("Job not started")


class NoConversationFoundError(BaseServiceError):
    http_status_code = status.HTTP_404_NOT_FOUND
    error_type = ErrorCode.NoConversationFoundError.name
    is_reliability_issue = False

    def __init__(self, account_id: AccountIdType, doc_id: int, conversation_id: int):
        self._account_id = account_id
        self._doc_id = doc_id
        self._conversation_id = conversation_id
        super().__init__()

    def _get_msg(self) -> str:
        return f"""No conversation found for account {self._account_id}
                and doc id {self._doc_id} and conversation id {self._conversation_id}"""
