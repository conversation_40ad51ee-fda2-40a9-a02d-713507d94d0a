from __future__ import annotations

import logging
from collections.abc import Sequence
from datetime import datetime
from typing import Any

from prime_db_utils import build_pagination_query
from prime_service_kit.fastapi_utils import pagination_args_type
from prime_shared.common_types import AccountIdType
from sqlalchemy import func
from sqlalchemy.exc import NoResultFound
from sqlmodel import asc, desc, select, update

from service.db.tables import DesignDocsTable
from service.models.design_docs import (
    AttackScenario,
    DesignDocPreviewDAL,
    DesignDocTopPolicyRecommendation,
    DesignDocTopRecommendation,
    DesignDocType,
)

from ..errors import DesignDocNotFoundError
from .dal_base import DalBase

LOGGER = logging.getLogger("design_docs_dal")


class DesignDocsDAL(DalBase):
    async def add_design_doc(  # noqa: PLR0913
        self,
        account_id: AccountIdType,
        title: str,
        created_by: str,
        *,
        origin_id: str | None = None,
        url: str | None = None,
        case_id: int | None = None,
        doc_source_type: DesignDocType = DesignDocType.ORIGINAL,
    ) -> DesignDocsTable:
        if case_id is not None:
            query = select(DesignDocsTable).where(
                DesignDocsTable.account_id == account_id,
                DesignDocsTable.case_id == case_id,
                DesignDocsTable.deleted_at.is_(None),  # type: ignore[union-attr]
            )
            result = await self._session.exec(query)
            existing = result.first()
            if existing is not None:
                raise ValueError(f"A design doc with account_id={account_id} and case_id={case_id} already exists.")

        design_doc = DesignDocsTable(
            account_id=account_id,
            title=title,
            created_by=created_by,
            file_origin_id=origin_id,
            url=url,
            case_id=case_id,
            doc_source_type=doc_source_type,
        )
        self._session.add(design_doc)
        await self._session.commit()
        return design_doc

    async def get_design_doc(
        self,
        account_id: AccountIdType,
        *,
        origin_id: str | None = None,
        url: str | None = None,
        case_id: int | None = None,
        include_deleted: bool = False,
    ) -> DesignDocsTable:
        query = select(DesignDocsTable).where(DesignDocsTable.account_id == account_id)
        if origin_id:
            query = query.where(DesignDocsTable.file_origin_id == origin_id)
        if url:
            query = query.where(DesignDocsTable.url == url)
        if case_id:
            query = query.where(DesignDocsTable.case_id == case_id)
        if not include_deleted:
            query = query.where(DesignDocsTable.deleted_at == None)  # noqa: E711
        try:
            return (await self._session.exec(query)).one()
        except NoResultFound:
            identifier = case_id if case_id is not None else (origin_id or url or "unknown")
            raise DesignDocNotFoundError(identifier) from None

    async def get_design_docs_by_id(self, account_id: AccountIdType, doc_id: int) -> DesignDocsTable:
        query = select(DesignDocsTable).where(
            DesignDocsTable.account_id == account_id,
            DesignDocsTable.id == doc_id,
            DesignDocsTable.deleted_at == None,  # noqa: E711
        )
        try:
            return (await self._session.exec(query)).one()
        except NoResultFound:
            raise DesignDocNotFoundError(doc_id) from None

    async def get_design_docs_preview_data(
        self,
        account_id: AccountIdType,
        doc_source_type: DesignDocType | None = None,
        pagination_args: pagination_args_type | None = None,
    ) -> list[DesignDocPreviewDAL]:
        query = select(  # type: ignore[call-overload, misc]
            DesignDocsTable.id,
            DesignDocsTable.title,
            DesignDocsTable.created_by,
            DesignDocsTable.created_at,
            DesignDocsTable.updated_at,
            DesignDocsTable.file_origin_id,
            DesignDocsTable.url,
            DesignDocsTable.case_id,
            DesignDocsTable.doc_source_type,
        ).where(
            DesignDocsTable.account_id == account_id,
            DesignDocsTable.deleted_at.is_(None),  # type: ignore[union-attr]
        )
        if doc_source_type:
            query = query.where(DesignDocsTable.doc_source_type == doc_source_type)
        if pagination_args:
            query = build_pagination_query(DesignDocsTable, pagination_args, query)
        query = query.order_by(desc(DesignDocsTable.created_at))
        query_result = await self._session.exec(query)

        result = [
            DesignDocPreviewDAL(
                id=row[0],
                title=row[1],
                created_by=row[2],
                created_at=row[3],
                updated_at=row[4],
                file_origin_id=row[5],
                url=row[6],
                case_id=row[7],
                doc_source_type=row[8],
            )
            for row in query_result.all()
        ]
        return result

    async def get_design_docs(
        self,
        account_id: AccountIdType,
        doc_source_type: DesignDocType | None = None,
        pagination_args: pagination_args_type | None = None,
    ) -> Sequence[DesignDocsTable]:
        query = (
            select(DesignDocsTable).where(DesignDocsTable.account_id == account_id, DesignDocsTable.deleted_at == None)  # noqa: E711
        )
        if doc_source_type:
            query = query.where(DesignDocsTable.doc_source_type == doc_source_type)
        if pagination_args:
            query = build_pagination_query(DesignDocsTable, pagination_args, query)
        query = query.order_by(asc(DesignDocsTable.created_at))
        return (await self._session.exec(query)).all()

    async def get_design_docs_count(
        self, account_id: AccountIdType, doc_source_type: DesignDocType | None = None
    ) -> int:
        query = select(DesignDocsTable).where(
            DesignDocsTable.account_id == account_id,
            DesignDocsTable.deleted_at == None,  # noqa: E711
        )
        if doc_source_type:
            query = query.where(DesignDocsTable.doc_source_type == doc_source_type)
        count = await self._session.scalar(select(func.count()).where().select_from(query.subquery()))
        return count or 0

    async def delete_design_doc_by_id(self, account_id: AccountIdType, doc_id: int) -> None:
        design_doc = await self.get_design_docs_by_id(account_id, doc_id)
        design_doc.deleted_at = datetime.now()
        await self._session.commit()

    async def update_design_doc_fields(
        self,
        design_doc_id: int,
        account_id: AccountIdType,
        **table_fields: Any,
    ) -> None:
        LOGGER.info("Updating design doc %s with %s", design_doc_id, table_fields)
        query = (
            update(DesignDocsTable)
            .where(DesignDocsTable.id == design_doc_id, DesignDocsTable.account_id == account_id)  # type: ignore[arg-type]
            .values(**table_fields)
        )
        await self._session.exec(query)  # type: ignore[call-overload]
        await self._session.commit()

    async def update_design_doc(  # noqa: PLR0913
        self,
        account_id: AccountIdType,
        design_doc_id: int,
        *,
        summary: str | None = None,
        description: str | None = None,
        top_recommendations: list[DesignDocTopRecommendation] | None = None,
        policy_recommendations: list[DesignDocTopPolicyRecommendation] | None = None,
        mermaid_diagram: str | None = None,
        title: str | None = None,
        attack_scenarios: list[AttackScenario] | None = None,
        dataflow_diagram: str | None = None,
    ) -> None:
        kwargs: dict[str, Any] = {}
        if summary:
            kwargs["summary"] = summary
        if description:
            kwargs["description"] = description
        if top_recommendations:
            kwargs["top_recommendations"] = top_recommendations
        if policy_recommendations:
            kwargs["policy_recommendations"] = policy_recommendations
        if mermaid_diagram:
            kwargs["mermaid_diagram"] = mermaid_diagram
        if title:
            kwargs["title"] = title
        if attack_scenarios:
            kwargs["attack_scenarios"] = attack_scenarios
        if dataflow_diagram:
            kwargs["attack_scenario_dataflow_diagram"] = dataflow_diagram
        await self.update_design_doc_fields(design_doc_id, account_id, **kwargs)
